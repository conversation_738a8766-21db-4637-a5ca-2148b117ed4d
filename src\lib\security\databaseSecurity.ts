/**
 * Database Security and Access Control
 * Secure database operations with access control and query protection
 */

import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// User access levels
export enum AccessLevel {
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin'
}

// Resource types
export enum ResourceType {
  USER = 'user',
  EMAIL = 'email',
  CALENDAR = 'calendar',
  CHAT = 'chat',
  DRAFT = 'draft'
}

// Access control interface
interface AccessControl {
  userId: string
  resourceType: ResourceType
  resourceId?: string
  requiredLevel: AccessLevel
}

// Query validation schemas
const querySchemas = {
  userQuery: z.object({
    id: z.string().uuid().optional(),
    email: z.string().email().optional(),
    limit: z.number().min(1).max(100).default(50),
    offset: z.number().min(0).default(0)
  }),
  
  emailQuery: z.object({
    userId: z.string().uuid(),
    messageId: z.string().optional(),
    threadId: z.string().optional(),
    limit: z.number().min(1).max(100).default(50),
    offset: z.number().min(0).default(0)
  }),
  
  calendarQuery: z.object({
    userId: z.string().uuid(),
    eventId: z.string().optional(),
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
    limit: z.number().min(1).max(100).default(50)
  })
}

/**
 * Check if user has access to resource
 */
export async function checkAccess(accessControl: AccessControl): Promise<boolean> {
  try {
    const { userId, resourceType, resourceId, requiredLevel } = accessControl
    
    // Admin users have full access (if you implement admin roles)
    // const user = await prisma.user.findUnique({ where: { id: userId } })
    // if (user?.role === 'admin') return true
    
    switch (resourceType) {
      case ResourceType.USER:
        // Users can only access their own data
        return resourceId === userId
        
      case ResourceType.EMAIL:
        // Check if email belongs to user
        if (resourceId) {
          const email = await prisma.user.findFirst({
            where: { 
              id: userId,
              // Add email ownership check here if you have email records
            }
          })
          return !!email
        }
        return true // For general email access
        
      case ResourceType.CALENDAR:
        // Check if calendar event belongs to user
        if (resourceId) {
          const event = await prisma.user.findFirst({
            where: { 
              id: userId,
              // Add calendar event ownership check here
            }
          })
          return !!event
        }
        return true // For general calendar access
        
      case ResourceType.CHAT:
        // Check if chat session belongs to user
        if (resourceId) {
          const chat = await prisma.chatSession.findFirst({
            where: { 
              id: resourceId,
              userId: userId
            }
          })
          return !!chat
        }
        return true // For general chat access
        
      case ResourceType.DRAFT:
        // Check if draft belongs to user
        if (resourceId) {
          const draft = await prisma.emailDraft.findFirst({
            where: { 
              id: resourceId,
              userId: userId
            }
          })
          return !!draft
        }
        return true // For general draft access
        
      default:
        return false
    }
  } catch (error) {
    console.error('❌ Access check error:', error)
    return false
  }
}

/**
 * Secure user query with access control
 */
export async function secureUserQuery(
  userId: string,
  query: any,
  requiredLevel: AccessLevel = AccessLevel.READ
) {
  try {
    // Validate query parameters
    const validatedQuery = querySchemas.userQuery.parse(query)
    
    // Check access
    const hasAccess = await checkAccess({
      userId,
      resourceType: ResourceType.USER,
      resourceId: validatedQuery.id || userId,
      requiredLevel
    })
    
    if (!hasAccess) {
      throw new Error('Access denied')
    }
    
    // Execute secure query
    const user = await prisma.user.findUnique({
      where: { id: validatedQuery.id || userId },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        gmailConnected: true,
        calendarConnected: true,
        createdAt: true,
        updatedAt: true,
        // Exclude sensitive fields like refresh tokens
      }
    })
    
    return user
  } catch (error) {
    console.error('❌ Secure user query error:', error)
    throw error
  }
}

/**
 * Secure email query with access control
 */
export async function secureEmailQuery(
  userId: string,
  query: any,
  requiredLevel: AccessLevel = AccessLevel.READ
) {
  try {
    // Validate query parameters
    const validatedQuery = querySchemas.emailQuery.parse({
      ...query,
      userId
    })
    
    // Check access
    const hasAccess = await checkAccess({
      userId,
      resourceType: ResourceType.EMAIL,
      resourceId: validatedQuery.messageId,
      requiredLevel
    })
    
    if (!hasAccess) {
      throw new Error('Access denied')
    }
    
    // For now, return query parameters since emails are stored externally
    // In the future, if you store email metadata in database:
    // const emails = await prisma.email.findMany({
    //   where: { userId },
    //   take: validatedQuery.limit,
    //   skip: validatedQuery.offset
    // })
    
    return validatedQuery
  } catch (error) {
    console.error('❌ Secure email query error:', error)
    throw error
  }
}

/**
 * Secure calendar query with access control
 */
export async function secureCalendarQuery(
  userId: string,
  query: any,
  requiredLevel: AccessLevel = AccessLevel.READ
) {
  try {
    // Validate query parameters
    const validatedQuery = querySchemas.calendarQuery.parse({
      ...query,
      userId
    })
    
    // Check access
    const hasAccess = await checkAccess({
      userId,
      resourceType: ResourceType.CALENDAR,
      resourceId: validatedQuery.eventId,
      requiredLevel
    })
    
    if (!hasAccess) {
      throw new Error('Access denied')
    }
    
    // For now, return query parameters since calendar events are stored externally
    // In the future, if you store calendar metadata in database:
    // const events = await prisma.calendarEvent.findMany({
    //   where: { userId },
    //   take: validatedQuery.limit
    // })
    
    return validatedQuery
  } catch (error) {
    console.error('❌ Secure calendar query error:', error)
    throw error
  }
}

/**
 * Secure chat session query
 */
export async function secureChatQuery(
  userId: string,
  sessionId?: string,
  requiredLevel: AccessLevel = AccessLevel.READ
) {
  try {
    // Check access
    const hasAccess = await checkAccess({
      userId,
      resourceType: ResourceType.CHAT,
      resourceId: sessionId,
      requiredLevel
    })
    
    if (!hasAccess) {
      throw new Error('Access denied')
    }
    
    if (sessionId) {
      // Get specific session
      const session = await prisma.chatSession.findFirst({
        where: { 
          id: sessionId,
          userId: userId
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            take: 100 // Limit messages for performance
          }
        }
      })
      return session
    } else {
      // Get user's sessions
      const sessions = await prisma.chatSession.findMany({
        where: { userId },
        orderBy: { updatedAt: 'desc' },
        take: 50,
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: { messages: true }
          }
        }
      })
      return sessions
    }
  } catch (error) {
    console.error('❌ Secure chat query error:', error)
    throw error
  }
}

/**
 * Secure draft query
 */
export async function secureDraftQuery(
  userId: string,
  draftId?: string,
  requiredLevel: AccessLevel = AccessLevel.READ
) {
  try {
    // Check access
    const hasAccess = await checkAccess({
      userId,
      resourceType: ResourceType.DRAFT,
      resourceId: draftId,
      requiredLevel
    })
    
    if (!hasAccess) {
      throw new Error('Access denied')
    }
    
    if (draftId) {
      // Get specific draft
      const draft = await prisma.emailDraft.findFirst({
        where: { 
          id: draftId,
          userId: userId
        }
      })
      return draft
    } else {
      // Get user's drafts
      const drafts = await prisma.emailDraft.findMany({
        where: { userId },
        orderBy: { updatedAt: 'desc' },
        take: 50
      })
      return drafts
    }
  } catch (error) {
    console.error('❌ Secure draft query error:', error)
    throw error
  }
}

/**
 * Log security events
 */
export function logSecurityEvent(
  userId: string,
  action: string,
  resourceType: ResourceType,
  resourceId?: string,
  metadata?: any
) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    userId,
    action,
    resourceType,
    resourceId,
    metadata
  }
  
  // In production, send to security monitoring system
  console.log('🔒 Security Event:', JSON.stringify(logEntry))
}

/**
 * Sanitize database input
 */
export function sanitizeDbInput(input: any): any {
  if (typeof input === 'string') {
    return input.trim().slice(0, 10000) // Prevent extremely long strings
  }
  
  if (Array.isArray(input)) {
    return input.slice(0, 1000).map(sanitizeDbInput) // Limit array size
  }
  
  if (input && typeof input === 'object') {
    const sanitized: any = {}
    for (const [key, value] of Object.entries(input)) {
      if (key.length <= 100) { // Limit key length
        sanitized[key] = sanitizeDbInput(value)
      }
    }
    return sanitized
  }
  
  return input
}
