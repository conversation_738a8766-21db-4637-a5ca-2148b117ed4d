/**
 * API Security Implementation
 * Comprehensive security measures for API endpoints
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { authenticateRequest } from './sessionManager'
import { secureConfig } from './secretsManager'

// Request validation schemas
export const commonSchemas = {
  // Email validation
  email: z.string().email('Invalid email format'),
  
  // ID validation
  id: z.string().min(1, 'ID is required'),
  
  // Pagination
  pagination: z.object({
    limit: z.number().min(1).max(100).default(50),
    offset: z.number().min(0).default(0)
  }),
  
  // Date range
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional()
  }),
  
  // Gmail specific
  gmailMessage: z.object({
    to: z.array(z.string().email()).min(1, 'At least one recipient required'),
    subject: z.string().min(1, 'Subject is required').max(998, 'Subject too long'),
    body: z.string().max(50000, 'Message body too long'),
    cc: z.array(z.string().email()).optional(),
    bcc: z.array(z.string().email()).optional()
  }),
  
  // Calendar event
  calendarEvent: z.object({
    title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
    description: z.string().max(8192, 'Description too long').optional(),
    startTime: z.string().datetime('Invalid start time'),
    endTime: z.string().datetime('Invalid end time'),
    attendees: z.array(z.string().email()).optional()
  })
}

// Security configuration
const SECURITY_CONFIG = {
  maxRequestSize: 10 * 1024 * 1024, // 10MB
  rateLimits: {
    default: { requests: 100, window: 15 * 60 * 1000 }, // 100 requests per 15 minutes
    auth: { requests: 10, window: 15 * 60 * 1000 }, // 10 auth requests per 15 minutes
    email: { requests: 50, window: 60 * 60 * 1000 }, // 50 emails per hour
    calendar: { requests: 200, window: 15 * 60 * 1000 } // 200 calendar requests per 15 minutes
  },
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  trustedProxies: ['127.0.0.1', '::1']
}

// Rate limiting storage
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

/**
 * Validate request method
 */
export function validateMethod(request: NextRequest, allowedMethods: string[] = SECURITY_CONFIG.allowedMethods): boolean {
  return allowedMethods.includes(request.method)
}

/**
 * Validate request size
 */
export function validateRequestSize(request: NextRequest): boolean {
  const contentLength = request.headers.get('content-length')
  if (contentLength) {
    const size = parseInt(contentLength)
    return size <= SECURITY_CONFIG.maxRequestSize
  }
  return true
}

/**
 * Rate limiting implementation
 */
export function checkRateLimit(
  request: NextRequest, 
  category: keyof typeof SECURITY_CONFIG.rateLimits = 'default'
): { allowed: boolean; remaining: number; resetTime: number } {
  const ip = getClientIP(request)
  const key = `${ip}:${category}`
  const limit = SECURITY_CONFIG.rateLimits[category]
  const now = Date.now()
  
  const current = rateLimitStore.get(key)
  
  if (!current || current.resetTime < now) {
    // New window
    const resetTime = now + limit.window
    rateLimitStore.set(key, { count: 1, resetTime })
    return { allowed: true, remaining: limit.requests - 1, resetTime }
  }
  
  if (current.count >= limit.requests) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime }
  }
  
  current.count++
  return { allowed: true, remaining: limit.requests - current.count, resetTime: current.resetTime }
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  // Check for forwarded IP from trusted proxies
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    const ips = forwarded.split(',').map(ip => ip.trim())
    return ips[0] // First IP in the chain
  }
  
  return realIP || remoteAddr || 'unknown'
}

/**
 * Sanitize input data
 */
export function sanitizeInput(data: any): any {
  if (typeof data === 'string') {
    return data
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim()
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeInput)
  }
  
  if (data && typeof data === 'object') {
    const sanitized: any = {}
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value)
    }
    return sanitized
  }
  
  return data
}

/**
 * Validate and sanitize request body
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; error: string }> {
  try {
    if (!request.body) {
      return { success: false, error: 'Request body is required' }
    }
    
    const contentType = request.headers.get('content-type')
    if (!contentType?.includes('application/json')) {
      return { success: false, error: 'Content-Type must be application/json' }
    }
    
    const rawData = await request.json()
    const sanitizedData = sanitizeInput(rawData)
    
    const result = schema.safeParse(sanitizedData)
    
    if (result.success) {
      return { success: true, data: result.data }
    } else {
      const errorMessage = result.error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join(', ')
      return { success: false, error: errorMessage }
    }
  } catch (error) {
    return { success: false, error: 'Invalid JSON in request body' }
  }
}

/**
 * Create secure API handler wrapper
 */
export function createSecureApiHandler<T = any>(
  handler: (request: NextRequest, context: { user: any; body?: T }) => Promise<NextResponse>,
  options: {
    requireAuth?: boolean
    schema?: z.ZodSchema<T>
    rateLimit?: keyof typeof SECURITY_CONFIG.rateLimits
    allowedMethods?: string[]
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // Method validation
      if (!validateMethod(request, options.allowedMethods)) {
        return NextResponse.json(
          { error: 'Method not allowed' },
          { status: 405 }
        )
      }
      
      // Request size validation
      if (!validateRequestSize(request)) {
        return NextResponse.json(
          { error: 'Request too large' },
          { status: 413 }
        )
      }
      
      // Rate limiting
      if (options.rateLimit) {
        const rateLimit = checkRateLimit(request, options.rateLimit)
        if (!rateLimit.allowed) {
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            { 
              status: 429,
              headers: {
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': rateLimit.resetTime.toString()
              }
            }
          )
        }
      }
      
      // Authentication
      let user: any = null
      if (options.requireAuth !== false) {
        const authResult = await authenticateRequest(request)
        if (!authResult.success) {
          return authResult.response
        }
        user = authResult.user
      }
      
      // Body validation
      let validatedBody: T | undefined
      if (options.schema && (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH')) {
        const bodyResult = await validateRequestBody(request, options.schema)
        if (!bodyResult.success) {
          return NextResponse.json(
            { error: bodyResult.error },
            { status: 400 }
          )
        }
        validatedBody = bodyResult.data
      }
      
      // Execute handler
      const response = await handler(request, { user, body: validatedBody })
      
      // Add security headers
      response.headers.set('X-Content-Type-Options', 'nosniff')
      response.headers.set('X-Frame-Options', 'DENY')
      response.headers.set('X-XSS-Protection', '1; mode=block')
      
      return response
      
    } catch (error) {
      console.error('❌ API handler error:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

/**
 * Cleanup expired rate limit entries
 */
export function cleanupRateLimits(): void {
  const now = Date.now()
  for (const [key, data] of rateLimitStore.entries()) {
    if (data.resetTime < now) {
      rateLimitStore.delete(key)
    }
  }
}

// Cleanup every 10 minutes
setInterval(cleanupRateLimits, 10 * 60 * 1000)
