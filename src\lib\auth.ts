import GoogleProvider from "next-auth/providers/google"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import { NextAuthOptions } from "next-auth"
import { secureConfig } from "@/lib/security/secretsManager"
import { generateSessionToken } from "@/lib/security/sessionManager"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  secret: secureConfig.nextAuthSecret,
  providers: [
    GoogleProvider({
      clientId: secureConfig.googleClientId,
      clientSecret: secureConfig.googleClientSecret,
      authorization: {
        params: {
          scope: "openid email profile https://mail.google.com/ https://www.googleapis.com/auth/calendar",
          access_type: "offline", // This requests a refresh token
          prompt: "consent", // This forces the consent screen to show, ensuring we get a refresh token
        },
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }: any) {
      try {
        // Enhanced security validation
        if (!user?.email || !account) {
          console.log('❌ Sign-in rejected: Missing user email or account')
          return false
        }

        // Validate email domain if needed (optional)
        // const allowedDomains = ['gmail.com', 'company.com']
        // const emailDomain = user.email.split('@')[1]
        // if (!allowedDomains.includes(emailDomain)) {
        //   console.log('❌ Sign-in rejected: Email domain not allowed')
        //   return false
        // }

        console.log('✅ Sign-in approved for:', user.email)
        return true
      } catch (error) {
        console.error('❌ Sign-in callback error:', error)
        return false
      }
    },
    async session({ session, user }: any) {
      try {
        if (session.user && user) {
          session.user.id = user.id

          // Get user data efficiently (cached in production)
          const userData = await prisma.user.findUnique({
            where: { id: user.id },
            select: {
              gmailConnected: true,
              gmailRefreshToken: true,
              calendarConnected: true,
              calendarRefreshToken: true
            }
          })

          session.user.gmailConnected = userData?.gmailConnected || false
          session.user.calendarConnected = userData?.gmailConnected || false

          // Generate session token for API authentication
          try {
            const sessionToken = await generateSessionToken(user.id)
            session.sessionToken = sessionToken
          } catch (error) {
            console.error('❌ Failed to generate session token:', error)
            // Don't fail the session, just log the error
          }
        }
        return session
      } catch (error) {
        console.error('❌ Session callback error:', error)
        return session
      }
    },
    async jwt({ token, account }: any) {
      try {
        if (account) {
          // Store OAuth tokens securely
          token.accessToken = account.access_token
          token.refreshToken = account.refresh_token
          token.expiresAt = account.expires_at

          // Add security metadata
          token.provider = account.provider
          token.scope = account.scope
        }
        return token
      } catch (error) {
        console.error('❌ JWT callback error:', error)
        return token
      }
    },
  },
  events: {
    async linkAccount({ user, account }: any) {
      // Handle Gmail and Calendar token storage after account is successfully linked
      if (account?.provider === "google") {
        console.log("🔗 Account linked for user:", user.email)
        
        // Check if this Google account has the required scopes
        const hasGmailScope = account.scope?.includes("https://mail.google.com/")
        const hasCalendarScope = account.scope?.includes("https://www.googleapis.com/auth/calendar")

        console.log("🔍 Scope check:", {
          fullScope: account.scope,
          hasGmailScope,
          hasCalendarScope,
          hasRefreshToken: !!account.refresh_token,
          hasAccessToken: !!account.access_token
        })
        
        try {
          // Update user with service tokens after account linking
          const updateData: any = {
            dailySendLimit: 250,
            dailySendCount: 0,
            lastSendReset: new Date()
          }
          
          // Handle Gmail connection
          if (hasGmailScope) {
            updateData.gmailConnected = true
            if (account.refresh_token) {
              updateData.gmailRefreshToken = account.refresh_token
              console.log("💾 Storing Gmail refresh token for user:", user.email)
            }
          }
          
          // Handle Calendar connection
          if (hasCalendarScope) {
            updateData.calendarConnected = true
            if (account.refresh_token) {
              updateData.calendarRefreshToken = account.refresh_token
              console.log("📅 Storing Calendar refresh token for user:", user.email)
            }
          }
          

          
          console.log("🔄 Updating user with data:", updateData)
          
          // Use user ID instead of email for more reliable updates
          const updatedUser = await prisma.user.update({
            where: { id: user.id },
            data: updateData
          })
          
          console.log("✅ User updated successfully:", {
            userId: updatedUser.id,
            gmailConnected: updatedUser.gmailConnected,
            hasGmailToken: !!updatedUser.gmailRefreshToken
          })
          
        } catch (error) {
          console.error("❌ Error saving Google tokens:", error)
        }
      }
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "database" as const,
  },
  debug: process.env.NODE_ENV === "development",
}
