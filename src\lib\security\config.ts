/**
 * Enhanced Security Configuration
 * Centralized security settings and monitoring
 */

import { secureConfig } from './secretsManager'

// Security levels
export enum SecurityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Security configuration
export const SECURITY_CONFIG = {
  // Session management
  SESSION_TOKEN_EXPIRY: 15 * 60 * 1000, // 15 minutes
  JWT_TOKEN_EXPIRY: 60 * 60 * 1000, // 1 hour
  SESSION_CLEANUP_INTERVAL: 60 * 60 * 1000, // 1 hour
  
  // Rate limiting (per IP)
  RATE_LIMITS: {
    default: { requests: 100, window: 15 * 60 * 1000 }, // 100/15min
    auth: { requests: 10, window: 15 * 60 * 1000 }, // 10/15min
    email: { requests: 50, window: 60 * 60 * 1000 }, // 50/hour
    calendar: { requests: 200, window: 15 * 60 * 1000 }, // 200/15min
    chat: { requests: 100, window: 60 * 1000 } // 100/minute
  },
  
  // Request validation
  MAX_REQUEST_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_JSON_SIZE: 1024 * 1024, // 1MB
  MAX_QUERY_PARAMS: 50,
  MAX_HEADER_SIZE: 8192, // 8KB
  
  // CORS settings
  CORS_ORIGINS: secureConfig.isProduction 
    ? secureConfig.allowedOrigins
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    
  // Security headers
  SECURITY_HEADERS: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': secureConfig.isProduction 
      ? 'max-age=********; includeSubDomains; preload' 
      : undefined
  },
  
  // Content Security Policy
  CSP: {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'https://accounts.google.com'],
    'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
    'font-src': ["'self'", 'https://fonts.gstatic.com'],
    'img-src': ["'self'", 'data:', 'https:'],
    'connect-src': ["'self'", 'https://api.gmail.com', 'https://www.googleapis.com'],
    'frame-src': ["'self'", 'https://accounts.google.com'],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"]
  },
  
  // Monitoring and alerting
  MONITORING: {
    LOG_SECURITY_EVENTS: true,
    ALERT_THRESHOLDS: {
      FAILED_LOGINS: 5, // per 15 minutes
      RATE_LIMIT_HITS: 10, // per hour
      INVALID_TOKENS: 20, // per hour
      ACCESS_DENIED: 50 // per hour
    },
    LOG_RETENTION_DAYS: 90
  },
  
  // Database security
  DATABASE: {
    QUERY_TIMEOUT: 30000, // 30 seconds
    MAX_CONNECTIONS: 100,
    CONNECTION_TIMEOUT: 10000, // 10 seconds
    IDLE_TIMEOUT: 300000, // 5 minutes
    ENABLE_QUERY_LOGGING: !secureConfig.isProduction
  },
  
  // Encryption settings
  ENCRYPTION: {
    ALGORITHM: 'aes-256-gcm',
    KEY_LENGTH: 32,
    IV_LENGTH: 16,
    TAG_LENGTH: 16,
    SALT_ROUNDS: 12
  }
}

// Security event types
export enum SecurityEventType {
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  TOKEN_GENERATED = 'token_generated',
  TOKEN_VALIDATED = 'token_validated',
  TOKEN_EXPIRED = 'token_expired',
  TOKEN_INVALID = 'token_invalid',
  RATE_LIMIT_HIT = 'rate_limit_hit',
  ACCESS_DENIED = 'access_denied',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  SECURITY_VIOLATION = 'security_violation'
}

// Security monitoring interface
export interface SecurityEvent {
  type: SecurityEventType
  level: SecurityLevel
  userId?: string
  ip: string
  userAgent?: string
  resource?: string
  details?: any
  timestamp: Date
}

// Security metrics
export interface SecurityMetrics {
  totalRequests: number
  blockedRequests: number
  rateLimitHits: number
  failedAuthentications: number
  securityViolations: number
  averageResponseTime: number
  uptime: number
}

/**
 * Get current security level based on threat assessment
 */
export function getCurrentSecurityLevel(): SecurityLevel {
  // In production, this would analyze current threats and adjust accordingly
  if (secureConfig.isProduction) {
    return SecurityLevel.HIGH
  }
  return SecurityLevel.MEDIUM
}

/**
 * Check if security feature is enabled
 */
export function isSecurityFeatureEnabled(feature: string): boolean {
  const level = getCurrentSecurityLevel()
  
  switch (feature) {
    case 'rate_limiting':
      return level !== SecurityLevel.LOW
    case 'request_validation':
      return true
    case 'session_monitoring':
      return level === SecurityLevel.HIGH || level === SecurityLevel.CRITICAL
    case 'advanced_logging':
      return level === SecurityLevel.CRITICAL
    default:
      return true
  }
}

/**
 * Get security configuration for current environment
 */
export function getSecurityConfig() {
  const level = getCurrentSecurityLevel()
  const config = { ...SECURITY_CONFIG }
  
  // Adjust configuration based on security level
  if (level === SecurityLevel.HIGH || level === SecurityLevel.CRITICAL) {
    // Stricter rate limits
    config.RATE_LIMITS.default.requests = 50
    config.RATE_LIMITS.auth.requests = 5
    
    // Shorter session expiry
    config.SESSION_TOKEN_EXPIRY = 10 * 60 * 1000 // 10 minutes
  }
  
  if (level === SecurityLevel.CRITICAL) {
    // Very strict limits
    config.RATE_LIMITS.default.requests = 25
    config.SESSION_TOKEN_EXPIRY = 5 * 60 * 1000 // 5 minutes
    config.MAX_REQUEST_SIZE = 1024 * 1024 // 1MB
  }
  
  return config
}

/**
 * Log security event
 */
export function logSecurityEvent(event: SecurityEvent): void {
  const logEntry = {
    ...event,
    timestamp: event.timestamp.toISOString()
  }
  
  // In production, send to security monitoring system
  console.log(`🔒 Security Event [${event.level.toUpperCase()}]:`, JSON.stringify(logEntry))
  
  // Check if event requires immediate attention
  if (event.level === SecurityLevel.CRITICAL) {
    console.error('🚨 CRITICAL SECURITY EVENT:', logEntry)
    // In production, trigger alerts/notifications
  }
}

/**
 * Create security monitoring middleware
 */
export function createSecurityMonitor() {
  const metrics: SecurityMetrics = {
    totalRequests: 0,
    blockedRequests: 0,
    rateLimitHits: 0,
    failedAuthentications: 0,
    securityViolations: 0,
    averageResponseTime: 0,
    uptime: Date.now()
  }
  
  return {
    recordRequest: () => metrics.totalRequests++,
    recordBlocked: () => metrics.blockedRequests++,
    recordRateLimit: () => metrics.rateLimitHits++,
    recordAuthFailure: () => metrics.failedAuthentications++,
    recordViolation: () => metrics.securityViolations++,
    getMetrics: () => ({ ...metrics, uptime: Date.now() - metrics.uptime })
  }
}

// Global security monitor instance
export const securityMonitor = createSecurityMonitor()
